# 针对M1芯片优化的PHP5.6镜像
# 使用支持多架构的基础镜像
FROM --platform=linux/amd64 php:5.6-fpm-jessie

# 更换为国内镜像源，加速M1上的下载
RUN echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian/ jessie main contrib non-free" > /etc/apt/sources.list && \
    echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian/ jessie-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian/ jessie-backports main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.tuna.tsinghua.edu.cn/debian-security/ jessie/updates main contrib non-free" >> /etc/apt/sources.list

# 安装必要的系统工具和依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        make \
        libfreetype6-dev \
        libjpeg62-turbo-dev \
        libmcrypt-dev \
        libpng12-dev \
        libxml2-dev \
        libcurl4-openssl-dev \
        libssl-dev \
        libbz2-dev \
        libicu-dev \
        libzip-dev \
        zlib1g-dev \
        libmysqlclient-dev \
        libxslt1-dev \
        wget \
        && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ \
    && docker-php-ext-configure intl \
    && docker-php-ext-install -j$(nproc) \
        bcmath \
        ctype \
        curl \
        dom \
        ereg \
        filter \
        ftp \
        gd \
        gettext \
        iconv \
        intl \
        json \
        mbstring \
        mcrypt \
        mhash \
        mysql \
        mysqli \
        mysqlnd \
        pcntl \
        pdo \
        pdo_mysql \
        pdo_sqlite \
        Phar \
        posix \
        session \
        shmop \
        simplexml \
        soap \
        sockets \
        spl \
        sqlite3 \
        standard \
        sysvsem \
        tokenizer \
        xml \
        xmlreader \
        xmlrpc \
        xmlwriter \
        zip \
        zlib

# 处理M1上Zend Guard Loader的兼容性
RUN arch=$(uname -m) && \
    if [ "$arch" = "aarch64" ]; then \
        # 对于ARM架构，使用兼容模式下载x86版本
        wget -q -O /tmp/zend-loader-php5.6-linux-x86_64.tar.gz https://downloads.zend.com/guard/7.0.0/zend-loader-php5.6-linux-x86_64.tar.gz; \
    else \
        wget -q -O /tmp/zend-loader-php5.6-linux-x86_64.tar.gz https://downloads.zend.com/guard/7.0.0/zend-loader-php5.6-linux-x86_64.tar.gz; \
    fi && \
    tar -zxf /tmp/zend-loader-php5.6-linux-x86_64.tar.gz -C /tmp && \
    mkdir -p /usr/local/lib/php/extensions/no-debug-non-zts-20131226/ && \
    cp /tmp/zend-loader-php5.6-linux-x86_64/ZendGuardLoader.so /usr/local/lib/php/extensions/no-debug-non-zts-20131226/ && \
    rm -rf /tmp/zend-loader-php5.6-linux-x86_64*

# 配置Zend Guard Loader
RUN echo "zend_extension=/usr/local/lib/php/extensions/no-debug-non-zts-20131226/ZendGuardLoader.so" > /usr/local/etc/php/conf.d/zend-guard-loader.ini \
    && echo "zend_loader.enable=1" >> /usr/local/etc/php/conf.d/zend-guard-loader.ini

# 设置工作目录
WORKDIR /var/www/html

# 暴露端口
EXPOSE 9000

# 启动命令
CMD ["php-fpm"]
