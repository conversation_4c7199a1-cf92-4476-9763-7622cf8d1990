# PHP 5.6 镜像
FROM --platform=linux/amd64 php:5.6-fpm

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive

# 配置Debian归档源（因为Stretch已经过期）
RUN echo "deb [trusted=yes] http://archive.debian.org/debian stretch main" > /etc/apt/sources.list && \
    echo "deb [trusted=yes] http://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list && \
    echo "Acquire::Check-Valid-Until false;" > /etc/apt/apt.conf.d/99no-check-valid-until

# 更新包列表并安装必要的系统工具和依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        make \
        libfreetype6-dev \
        libjpeg62-turbo-dev \
        libmcrypt-dev \
        libpng-dev \
        libxml2-dev \
        libcurl4-openssl-dev \
        libssl-dev \
        libbz2-dev \
        libicu-dev \
        zlib1g-dev \
        default-libmysqlclient-dev \
        libxslt1-dev \
        libsqlite3-dev \
        wget \
        && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ \
    && docker-php-ext-configure intl \
    && docker-php-ext-install -j$(nproc) \
        bcmath \
        bz2 \
        curl \
        dom \
        ftp \
        gd \
        gettext \
        iconv \
        intl \
        json \
        mbstring \
        mcrypt \
        mysql \
        mysqli \
        pcntl \
        pdo \
        pdo_mysql \
        pdo_sqlite \
        simplexml \
        soap \
        sockets \
        sqlite3 \
        tokenizer \
        xml \
        xmlreader \
        xmlrpc \
        xmlwriter \
        xsl

# 设置工作目录
WORKDIR /web

# 暴露端口
EXPOSE 9000

# 启动命令
CMD ["php-fpm"]
