version: "3.8"
services:
  nginx:
    image: nginx
    privileged: true
    ports:
      - "80:80"
      - "443:443"
    container_name: nginx  
    volumes:
      - /Applications/MxSrvs/www:/usr/share/nginx/html
      - /Applications/MxSrvs/docker/nginxconf:/etc/nginx/conf.d
    networks:
      - web-net
  php:
    image: devilbox/php-fpm-8.1
    privileged: true
    container_name: php8
    volumes:
      - /Applications/MxSrvs/www:/web
    networks:
      - web-net
  php73:
    image: php:7.3-fpm
    container_name: php73
    volumes:
      - /Applications/MxSrvs/www:/web
    networks:
      - web-net
  php74:
    image: devilbox/php-fpm-7.4
    container_name: php74
    ports:
      - "10000:10000"
    volumes:
      - /Applications/MxSrvs/www:/web
    networks:
      - web-net
  redis:
    image: redis
    ports:
      - "6379:6379"
    container_name: redis
    volumes:
      - /Applications/MxSrvs/docker/redis/data:/data
    networks:
      - web-net

networks:
  web-net:
