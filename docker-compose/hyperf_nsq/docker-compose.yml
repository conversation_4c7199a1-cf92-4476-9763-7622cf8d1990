version: "3.8"
services:
  nsqlookupd:
    container_name: nsqlookupd
    image: nsqio/nsq
    command: /nsqlookupd
    ports:
      - "4160"
      - "4161"
    networks:
      - hyperf
  nsqd:
    container_name: nsqd
    image: nsqio/nsq
    command: /nsqd --lookupd-tcp-address=nsqlookupd:4160
    depends_on:
      - nsqlookupd
    ports:
      - "4150"
      - "4151"
    networks:
      - hyperf
  nsqadmin:
    container_name: nsqadmin
    image: nsqio/nsq
    command: /nsqadmin --lookupd-http-address=nsqlookupd:4161
    depends_on:
      - nsqlookupd  
    ports:
      - "4171"
    networks:
      - hyperf

networks:
  hyperf: