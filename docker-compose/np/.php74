# 使用官方 PHP 7.4 FPM 镜像作为基础镜像
FROM php:7.4-fpm

# 安装常用 PHP 扩展和 Redis 扩展
RUN apt-get update && apt-get install -y \
    zlib1g-dev \
    libzip-dev \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libxml2-dev \
    libssl-dev \
    libcurl4-openssl-dev \
    libonig-dev \
    unzip \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    && docker-php-ext-install pdo_mysql bcmath zip gd mysqli soap curl mbstring xml pcntl bcmath \
    && pecl install redis \
    && docker-php-ext-enable redis
    && docker-php-ext-enable bcmath

# 设置工作目录
WORKDIR /var/www/html

# 暴露 PHP-FPM 端口
EXPOSE 9000
