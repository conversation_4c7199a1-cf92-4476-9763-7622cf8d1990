version: "3.8"
services:
  nginx:
    image: nginx
    privileged: true
    network_mode: host
    container_name: nginxh 
    volumes:
      - /Applications/MxSrvs/www:/usr/share/nginx/html
      - /Applications/MxSrvs/docker/nginxconf:/etc/nginx/conf.d
  php:
    image: devilbox/php-fpm-8.1
    privileged: true
    network_mode: host
    container_name: php8h
    volumes:
      - /Applications/MxSrvs/www:/web
  php56:
    build:
      context: .
      dockerfile: .php56
    network_mode: host
    container_name: php56h
    volumes:
      - /Applications/MxSrvs/www:/web
  php73:
    image: php:7.3-fpm
    container_name: php73h
    network_mode: host
    volumes:
      - /Applications/MxSrvs/www:/web
  php74:
    build:
      context: .
      dockerfile: .php74
    container_name: php74h
    network_mode: host
    volumes:
      - /Applications/MxSrvs/www:/web
  redis:
    image: redis
    network_mode: host
    container_name: redish
    volumes:
      - /Applications/MxSrvs/docker/redis/data:/data
