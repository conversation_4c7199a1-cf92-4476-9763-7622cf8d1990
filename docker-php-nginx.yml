version: "2.3"
services:
  nginx:
    image: nginx
    privileged: true
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /Applications/MxSrvs/www:/usr/share/nginx/html
      - /Applications/MxSrvs/docker/conf:/etc/nginx/conf.d
      - /Applications/MxSrvs/logs:/var/log/nginx
      - /Applications/MxSrvs/cert:/etc/nginx/cert
    networks:
      - web-net
  php:
    image: devilbox/php-fpm:7.3-base
    privileged: true
    volumes:
      - /Applications/MxSrvs/www:/web
    networks:
      - web-net
networks:
  web-net:
