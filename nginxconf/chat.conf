server {
        listen  80;
        server_name chat.leisheng.com;

        client_max_body_size 50m;
        client_body_buffer_size 50m;

        location / {
                root /usr/share/nginx/html/www/self;
                index index.php index.html index.htm;
                #try_files $uri $uri/ /index.php$is_args$args;
        }

        location ~ \.php(.*)$ {
                fastcgi_pass    php74:9000;
                fastcgi_index   index.php;
                fastcgi_param   SCRIPT_FILENAME /web/self$fastcgi_script_name;
                fastcgi_param   PATH_INFO $1;
                include         fastcgi_params;
        }
}

