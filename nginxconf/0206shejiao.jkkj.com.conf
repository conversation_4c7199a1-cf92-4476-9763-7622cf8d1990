server {
        listen  80;
        server_name 0206shejiao.jkkj.com;

        client_max_body_size 50m;
        client_body_buffer_size 50m;
        location / {
                root /usr/share/nginx/html/jkkj/0206shejiao/admin.meetov.com/public;
                index index.php index.html index.htm;
                #try_files $uri $uri/ /index.php$is_args$args;
                if (!-e $request_filename) {
                        rewrite ^(.*)$ /index.php?s=$1 last;
                        break;
                 }
        }
        location ~ \.php(.*)$ {
                fastcgi_pass    php74:9000;
                fastcgi_index   index.php;
                fastcgi_param   SCRIPT_FILENAME /web/jkkj/0206shejiao/admin.meetov.com/public$fastcgi_script_name;
                fastcgi_param   PATH_INFO $1;
                include         fastcgi_params;
        }
}

