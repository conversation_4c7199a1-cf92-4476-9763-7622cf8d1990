server {
        listen  80;
        server_name thinksns.leisheng.com;
        client_max_body_size 50m;
        client_body_buffer_size 50m;
        root /usr/share/nginx/html/2025/opppi;
        index index.php index.html index.htm;

        location / {
                try_files $uri $uri/ /index.php?$query_string;
        }

        location ~ \.php(.*)$ {
                fastcgi_pass    php56:9000;
                fastcgi_index   index.php;
                fastcgi_param   SCRIPT_FILENAME /web/2025/opppi$fastcgi_script_name;
                fastcgi_param   PATH_INFO $1;
                include         fastcgi_params;
        }
}

