# # 至少需要一个 Hyperf 节点，多个配置多行
# upstream hyperf {
#     # Hyperf HTTP Server 的 IP 及 端口
#     server hyperf_meetov:9510;
# }

# server {
#     # 监听端口
#     listen 80; 
#     # 绑定的域名，填写您的域名
#     server_name 0206api.jkkj.com;

#     location / {
#         add_header Access-Control-Allow-Origin *;
#         add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
#         add_header Access-Control-Allow-Headers 'DNT,Keep-Alive,User-Agent,Cache-Control,Content-Type,Authorization';

#         if ($request_method = 'OPTIONS') {
#             return 204;
#         }
#         # 执行代理访问真实服务器
#         proxy_pass http://hyperf;
#     }
# }
