server {
        listen  2300;
        server_name localhost;

        client_max_body_size 50m;
        client_body_buffer_size 50m;

        location / {
                root /usr/share/nginx/html/new_project/api/public;
                index index.php index.html index.htm;
                #try_files $uri $uri/ /index.php$is_args$args;
                if (!-e $request_filename) {
                        rewrite ^(.*)$ /index.php?s=$1 last;
                        break;
                 }
        }
        location ~ \.php$ {
                fastcgi_pass    php73:9000;
                fastcgi_index   index.php;
                fastcgi_param   SCRIPT_FILENAME /web/new_project/api/public$fastcgi_script_name;
                include         fastcgi_params;
        }
}
