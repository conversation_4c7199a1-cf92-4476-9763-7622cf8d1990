server {
        listen  80;
        server_name mdd.jkkj.com;

        client_max_body_size 50m;
        client_body_buffer_size 50m;

        # location / {
        #         root /usr/share/nginx/html/jkkj/maiduoduo/pro/public;
        #         index index.php index.html index.htm;
        #         #try_files $uri $uri/ /index.php$is_args$args;
        #         if (!-e $request_filename) {
        #                 rewrite ^(.*)$ /index.php?s=$1 last;
        #                 break;
        #          }
        # }

        #PROXY-START/
        location  ~* \.(php|jsp|cgi|asp|aspx)$
        {
                # proxy_pass http://pro_s:20199;
                proxy_pass http://php74:20199;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header REMOTE-HOST $remote_addr;
        }
        location /
        {
                if (!-e $request_filename) {
                        # proxy_pass http://pro_s:20199;
                        proxy_pass http://php74:20199;
                }
                proxy_http_version 1.1;
                proxy_read_timeout 360s;   
                proxy_redirect off; 
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header REMOTE-HOST $remote_addr;

                add_header X-Cache $upstream_cache_status;

                #Set Nginx Cache

                add_header Cache-Control no-cache;
                expires 12h;
        }
        #PROXY-END/


        # location ~ \.php(.*)$ {
        #         fastcgi_pass    pro_s:20199;
        #         fastcgi_index   index.php;
        #         fastcgi_param   SCRIPT_FILENAME /web/jkkj/maiduoduo/pro/public$fastcgi_script_name;
        #         fastcgi_param   PATH_INFO $1;
        #         include         fastcgi_params;
        # }
}

