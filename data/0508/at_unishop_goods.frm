TYPE=VIEW
query=select `o`.`createtime` AS `date_time`,`op`.`order_id` AS `order_id`,`op`.`product_id` AS `product_id`,`op`.`number` AS `number`,`p`.`title` AS `product_name` from ((`0508`.`at_unishop_order` `o` join `0508`.`at_unishop_order_product` `op` on((`o`.`id` = `op`.`order_id`))) left join `0508`.`at_unishop_product` `p` on((`op`.`product_id` = `p`.`id`))) where ((`o`.`status` = 1) and (`o`.`have_paid` <> 0))
md5=c35a5c005a25aecce5f4d605496d890f
updatable=0
algorithm=0
definer_user=root
definer_host=%
suid=1
with_check_option=0
timestamp=2024-07-24 02:26:31
create-version=1
source=select `o`.`createtime` AS `date_time`,`op`.`order_id` AS `order_id`,`op`.`product_id` AS `product_id`,`op`.`number` AS `number`,`p`.`title` AS `product_name` from ((`at_unishop_order` `o` join `at_unishop_order_product` `op` on((`o`.`id` = `op`.`order_id`))) left join `at_unishop_product` `p` on((`op`.`product_id` = `p`.`id`))) where ((`o`.`status` = 1) and (`o`.`have_paid` <> 0))
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_general_ci
view_body_utf8=select `o`.`createtime` AS `date_time`,`op`.`order_id` AS `order_id`,`op`.`product_id` AS `product_id`,`op`.`number` AS `number`,`p`.`title` AS `product_name` from ((`0508`.`at_unishop_order` `o` join `0508`.`at_unishop_order_product` `op` on((`o`.`id` = `op`.`order_id`))) left join `0508`.`at_unishop_product` `p` on((`op`.`product_id` = `p`.`id`))) where ((`o`.`status` = 1) and (`o`.`have_paid` <> 0))
